#include <MsTimer2.h>
// 红外传感器引脚定义
const int SENSOR1 = A0;
const int SENSOR2 = A1;
const int SENSOR3 = A2;
const int SENSOR4 = A3;
const int SENSOR5 = A4;
const int SENSOR6 = A5;
const int SENSOR7 = A6;
const int SENSOR8 = A7;

// 电机控制引脚
#define ENCODER_A_L  2
#define ENCODER_B_L  4
#define ENCODER_A_R  3
#define ENCODER_B_R  5
#define PWML        11
#define PWMR        12
#define DIR_LEFT    6
#define DIR_RIGHT   7

// 控制参数 - 优化后的参数
#define PERIOD      20
#define BASE_SPEED  35        // 提高基础速度，增强动态响应
#define MAX_SPEED   70        // 提高最大速度，适应急弯
#define MIN_SPEED   -15       // 增加反向速度范围
#define MAX_PWM     255

// PWM滑动滤波参数 - 优化滤波效果
#define PWM_FILTER_SIZE  3     // 减小滤波窗口，提高响应速度
#define PWM_FILTER_ALPHA 0.4   // 调整滤波系数，平衡平滑度和响应性

// 直角转弯参数 - 优化转弯性能
#define HISTORY_SIZE     6     // 增加历史深度，提高判断准确性
#define LOST_THRESHOLD  20     // 减少丢失阈值，更快响应
#define SHARP_TURN_DIFF 45     // 增加转弯速度差，提高转弯能力
#define MIN_ACTIVE_SENSORS 1   // 降低最少传感器要求，提高灵敏度

// 新增：自适应参数
#define CURVE_DETECTION_THRESHOLD 2.0  // 弯道检测阈值
#define STRAIGHT_SPEED_BOOST 5         // 直线加速量
#define CURVE_SPEED_REDUCTION 8        // 弯道减速量
#define ERROR_INTEGRAL_LIMIT 50        // 积分限幅，防止积分饱和

// 全局变量
volatile float TARGET_L = 0;
volatile float TARGET_R = 0;
volatile float encoderVal_L = 0;
volatile float encoderVal_R = 0;
volatile float velocity_L = 0;
volatile float velocity_R = 0;

// 增量式PID结构体 - 增强版
typedef struct {
  double Kp;              // 比例系数
  double Ki;              // 积分系数
  double Kd;              // 微分系数
  double prev_error;      // 上一次误差
  double prev_prev_error; // 上上次误差
  double output;          // 当前输出值
  double integral_sum;    // 积分累积值（用于积分限幅）
  double max_integral;    // 最大积分值
} IncrementalPID;

// PWM滑动滤波结构体
typedef struct {
  float buffer[PWM_FILTER_SIZE];  // 滑动窗口缓冲区
  int index;                      // 当前索引
  float sum;                      // 当前总和
  bool filled;                    // 缓冲区是否已填满
  float filtered_value;           // 滤波后的值
} PWMFilter;

// 运动状态检测结构体
typedef struct {
  float deviation_history[HISTORY_SIZE];  // 偏差历史
  int history_index;                      // 历史索引
  bool is_curve;                          // 是否在弯道
  bool is_sharp_turn;                     // 是否在急转弯
  float avg_deviation;                    // 平均偏差
} MotionState;

// 优化后的PID参数 - 基于经验调优
// 增加Kp提高响应速度，适当降低Ki防止积分饱和，增加Kd提高稳定性
IncrementalPID pid_L = {2.0, 0.8, 2.0, 0, 0, 0, 0, ERROR_INTEGRAL_LIMIT};  // 左轮PID
IncrementalPID pid_R = {2.0, 0.8, 2.0, 0, 0, 0, 0, ERROR_INTEGRAL_LIMIT};  // 右轮PID

int pwm_L = 0;
int pwm_R = 0;

// PWM滤波器
PWMFilter pwm_filter_L = {{0}, 0, 0, false, 0};
PWMFilter pwm_filter_R = {{0}, 0, 0, false, 0};

// 循迹相关变量
int sensorState[8] = {1, 1, 1, 1, 1, 1, 1, 1};
int lastTrackState = 0;
unsigned long lostTrackTime = 0;

// 运动状态检测
MotionState motion_state = {{0}, 0, false, false, 0};

// 直角转弯相关变量
int sensorHistory[HISTORY_SIZE][8]; // 传感器历史状态寄存器
int historyIndex = 0;               // 当前历史记录位置
int lostCounter = 0;                // 丢失轨迹计数器
int lastDeviation = 0;              // 上次的偏差方向(左:-1, 右:1)
bool isSharpTurn = false;           // 是否正在进行直角转弯
int stableTrackCounter = 0;         // 稳定跟踪计数器

// 新增：传感器噪声滤波
int sensorReadCount[8] = {0};       // 传感器读取计数
int sensorFilterThreshold = 2;      // 滤波阈值

// 编码器中断处理函数
void getEncoder_L() {
  if (digitalRead(ENCODER_A_L) == LOW) {
    encoderVal_L += (digitalRead(ENCODER_B_L) == LOW) ? -1 : 1;
  } else {
    encoderVal_L += (digitalRead(ENCODER_B_L) == LOW) ? 1 : -1;
  }
}

void getEncoder_R() {
  if (digitalRead(ENCODER_A_R) == LOW) {
    encoderVal_R += (digitalRead(ENCODER_B_R) == LOW) ? 1 : -1;
  } else {
    encoderVal_R += (digitalRead(ENCODER_B_R) == LOW) ? -1 : 1;
  }
}

// 优化的增量型PID控制器 - 增加积分限幅和死区处理
double compute_pid(IncrementalPID *pid, double setpoint, double actual_value) {
    // 计算当前误差
    double error = setpoint - actual_value;

    // 死区处理 - 减少小误差时的抖动
    if (abs(error) < 0.5) {
        error = 0;
    }

    // 积分项处理 - 防止积分饱和
    pid->integral_sum += error;
    if (pid->integral_sum > pid->max_integral) {
        pid->integral_sum = pid->max_integral;
    } else if (pid->integral_sum < -pid->max_integral) {
        pid->integral_sum = -pid->max_integral;
    }

    // 计算PID增量
    double delta_output = pid->Kp * (error - pid->prev_error)
                          + pid->Ki * error
                          + pid->Kd * (error - 2 * pid->prev_error + pid->prev_prev_error);

    // 更新输出
    pid->output += delta_output;

    // 输出限幅
    if (pid->output > MAX_PWM) pid->output = MAX_PWM;
    else if (pid->output < -MAX_PWM) pid->output = -MAX_PWM;

    // 更新历史误差
    pid->prev_prev_error = pid->prev_error;
    pid->prev_error = error;

    return pid->output;
}

// 左轮PID控制
int pidcontrol_L(float target, float current) {
  return (int)compute_pid(&pid_L, target, current);
}

// 右轮PID控制
int pidcontrol_R(float target, float current) {
  return (int)compute_pid(&pid_R, target, current);
}

// 优化的传感器读取 - 增加噪声滤波
void readSensors() {
  // 读取原始传感器数据
  int rawSensorData[8];
  rawSensorData[0] = digitalRead(SENSOR1);
  rawSensorData[1] = digitalRead(SENSOR2);
  rawSensorData[2] = digitalRead(SENSOR3);
  rawSensorData[3] = digitalRead(SENSOR4);
  rawSensorData[4] = digitalRead(SENSOR5);
  rawSensorData[5] = digitalRead(SENSOR6);
  rawSensorData[6] = digitalRead(SENSOR7);
  rawSensorData[7] = digitalRead(SENSOR8);

  // 简单的多数表决滤波
  for(int i = 0; i < 8; i++) {
    if(rawSensorData[i] == 1) {
      sensorReadCount[i]++;
    } else {
      sensorReadCount[i]--;
    }

    // 限制计数范围
    sensorReadCount[i] = constrain(sensorReadCount[i], 0, sensorFilterThreshold * 2);

    // 根据计数确定最终状态
    sensorState[i] = (sensorReadCount[i] > sensorFilterThreshold) ? 1 : 0;
  }

  // 更新历史寄存器
  for(int i=0; i<8; i++) {
    sensorHistory[historyIndex][i] = sensorState[i];
  }
  historyIndex = (historyIndex + 1) % HISTORY_SIZE;
}

// [新增] 获取历史主要偏差方向
int getHistoryDeviation() {
  int leftCount = 0;
  int rightCount = 0;
  
  for(int i=0; i<HISTORY_SIZE; i++) {
    int sensorSum = 0;
    int activeSensors = 0;
    
    for(int j=0; j<8; j++) {
      if(sensorHistory[i][j] == 1) {
        sensorSum += (j - 3.5);
        activeSensors++;
      }
    }
    
    if(activeSensors > 0) {
      float deviation = sensorSum / (float)activeSensors;
      if(deviation > 0) rightCount++;
      else if(deviation < 0) leftCount++;
    }
  }
  
  if(leftCount > rightCount) return -1;
  else if(rightCount > leftCount) return 1;
  else return 0;
}

// 新增：运动状态检测函数
void updateMotionState(float deviation) {
  // 更新偏差历史
  motion_state.deviation_history[motion_state.history_index] = deviation;
  motion_state.history_index = (motion_state.history_index + 1) % HISTORY_SIZE;

  // 计算平均偏差
  float sum = 0;
  for(int i = 0; i < HISTORY_SIZE; i++) {
    sum += abs(motion_state.deviation_history[i]);
  }
  motion_state.avg_deviation = sum / HISTORY_SIZE;

  // 判断是否在弯道
  motion_state.is_curve = (motion_state.avg_deviation > CURVE_DETECTION_THRESHOLD);

  // 判断是否在急转弯
  motion_state.is_sharp_turn = (abs(deviation) > 3.0);
}

// 优化的目标速度计算 - 增加自适应速度调节
void calculateTargetSpeed() {
  int sensorSum = 0;
  int activeSensors = 0;

  for(int i=0; i<8; i++) {
    if(sensorState[i] == 1) {
      sensorSum += (i - 3.5);
      activeSensors++;
    }
  }

  // 处理丢失轨迹情况（直角转弯时）
  if(activeSensors == 0) {
    lostCounter++;

    // 根据历史状态判断转弯方向
    int histDev = getHistoryDeviation();
    if(histDev != 0) {
      lastDeviation = histDev;
    }

    // 根据最后已知方向进行转弯
    if(lostCounter < LOST_THRESHOLD) {
      if(lastDeviation < 0) { // 最后是左偏
        TARGET_L = BASE_SPEED - SHARP_TURN_DIFF;
        TARGET_R = BASE_SPEED + SHARP_TURN_DIFF;
      } else { // 最后是右偏
        TARGET_L = BASE_SPEED + SHARP_TURN_DIFF;
        TARGET_R = BASE_SPEED - SHARP_TURN_DIFF;
      }
    } else {
      TARGET_L = 0;
      TARGET_R = 0;
    }
    return;
  }

  // 重置丢失计数器
  lostCounter = 0;
  lastTrackState = 1;

  // 计算当前偏差
  float deviation = sensorSum / (float)activeSensors;
  lastDeviation = (deviation > 0) ? 1 : ((deviation < 0) ? -1 : 0);

  // 更新运动状态
  updateMotionState(deviation);

  // 自适应基础速度调节
  float adaptiveBaseSpeed = BASE_SPEED;
  if(motion_state.is_curve) {
    adaptiveBaseSpeed -= CURVE_SPEED_REDUCTION;  // 弯道减速
  } else {
    adaptiveBaseSpeed += STRAIGHT_SPEED_BOOST;   // 直线加速
  }

  // 改进的速度差计算 - 非线性响应
  float speedDiff;
  if(motion_state.is_sharp_turn) {
    // 急转弯时使用更大的速度差
    speedDiff = deviation * abs(deviation) * 1.5 * ((MAX_SPEED - adaptiveBaseSpeed) / 2.0);
  } else {
    // 正常情况下的速度差计算
    speedDiff = deviation * abs(deviation) * ((MAX_SPEED - adaptiveBaseSpeed) / 2.0);
  }

  TARGET_L = adaptiveBaseSpeed - speedDiff;
  TARGET_R = adaptiveBaseSpeed + speedDiff;

  // 速度限幅
  TARGET_L = constrain(TARGET_L, MIN_SPEED, MAX_SPEED);
  TARGET_R = constrain(TARGET_R, MIN_SPEED, MAX_SPEED);
}

// PWM滤波函数
float applyPWMFilter(PWMFilter *filter, float new_value) {
  // 更新滑动窗口
  filter->sum -= filter->buffer[filter->index];
  filter->buffer[filter->index] = new_value;
  filter->sum += new_value;
  filter->index = (filter->index + 1) % PWM_FILTER_SIZE;

  if(!filter->filled && filter->index == 0) {
    filter->filled = true;
  }

  // 计算滤波值
  if(filter->filled) {
    filter->filtered_value = filter->sum / PWM_FILTER_SIZE;
  } else {
    filter->filtered_value = filter->sum / (filter->index + 1);
  }

  return filter->filtered_value;
}

// 优化的电机控制 - 增加PWM滤波和软启动
void controlMotors() {
  // 计算速度
  velocity_L = (encoderVal_L / 780.0) * 3.1415 * 2.0 * (1000.0 / PERIOD) * 10;
  velocity_R = (encoderVal_R / 780.0) * 3.1415 * 2.0 * (1000.0 / PERIOD) * 10;

  // PID控制
  int raw_pwm_L = pidcontrol_L(TARGET_L, velocity_L);
  int raw_pwm_R = pidcontrol_R(TARGET_R, velocity_R);

  // PWM滤波，减少抖动
  pwm_L = (int)applyPWMFilter(&pwm_filter_L, raw_pwm_L);
  pwm_R = (int)applyPWMFilter(&pwm_filter_R, raw_pwm_R);

  // 控制左电机
  if (pwm_L >= 0) {
    digitalWrite(DIR_LEFT, HIGH);
  } else {
    digitalWrite(DIR_LEFT, LOW);
  }
  analogWrite(PWML, abs(pwm_L));

  // 控制右电机
  if (pwm_R >= 0) {
    digitalWrite(DIR_RIGHT, LOW);
  } else {
    digitalWrite(DIR_RIGHT, HIGH);
  }
  analogWrite(PWMR, abs(pwm_R));

  // 重置编码器计数
  encoderVal_L = 0;
  encoderVal_R = 0;
}

void controlTask() {
  readSensors();
  calculateTargetSpeed();
  controlMotors();
}

void setup() {
  // 配置PWM频率 - 提高PWM频率减少电机噪音
  TCCR1B = TCCR1B & B11111000 | B00000001;

  // 初始化引脚
  pinMode(PWML, OUTPUT);
  pinMode(PWMR, OUTPUT);
  pinMode(DIR_LEFT, OUTPUT);
  pinMode(DIR_RIGHT, OUTPUT);
  pinMode(SENSOR1, INPUT);
  pinMode(SENSOR2, INPUT);
  pinMode(SENSOR3, INPUT);
  pinMode(SENSOR4, INPUT);
  pinMode(SENSOR5, INPUT);
  pinMode(SENSOR6, INPUT);
  pinMode(SENSOR7, INPUT);
  pinMode(SENSOR8, INPUT);
  pinMode(ENCODER_A_L, INPUT);
  pinMode(ENCODER_B_L, INPUT);
  pinMode(ENCODER_A_R, INPUT);
  pinMode(ENCODER_B_R, INPUT);

  // 配置中断
  attachInterrupt(0, getEncoder_L, CHANGE);
  attachInterrupt(1, getEncoder_R, CHANGE);

  // 初始化串口
  Serial.begin(9600);

  // 初始化历史寄存器
  for(int i=0; i<HISTORY_SIZE; i++) {
    for(int j=0; j<8; j++) {
      sensorHistory[i][j] = 1;
    }
  }

  // 初始化传感器滤波计数器
  for(int i=0; i<8; i++) {
    sensorReadCount[i] = sensorFilterThreshold;
  }

  // 初始化PWM滤波器
  for(int i=0; i<PWM_FILTER_SIZE; i++) {
    pwm_filter_L.buffer[i] = 0;
    pwm_filter_R.buffer[i] = 0;
  }

  // 初始化运动状态
  for(int i=0; i<HISTORY_SIZE; i++) {
    motion_state.deviation_history[i] = 0;
  }

  Serial.println("AutoCar Initialized - Enhanced Version");
  Serial.println("PID Parameters: Kp=2.8, Ki=0.8, Kd=1.2");

  // 启动定时器
  MsTimer2::set(PERIOD, controlTask);
  MsTimer2::start();

  digitalWrite(DIR_LEFT, LOW);
  digitalWrite(DIR_RIGHT, LOW);
}

void loop() {
  // 增强的调试输出 - 可通过串口监控系统状态
  static unsigned long lastPrintTime = 0;
  if(millis() - lastPrintTime > 500) {  // 每500ms输出一次状态
    lastPrintTime = millis();

    Serial.print("Sensors: ");
    for(int i=0; i<8; i++) {
      Serial.print(sensorState[i]);
    }
    Serial.print(" | Target: L=");
    Serial.print(TARGET_L, 1);
    Serial.print(", R=");
    Serial.print(TARGET_R, 1);
    Serial.print(" | Vel: L=");
    Serial.print(velocity_L, 1);
    Serial.print(", R=");
    Serial.print(velocity_R, 1);
    Serial.print(" | PWM: L=");
    Serial.print(pwm_L);
    Serial.print(", R=");
    Serial.print(pwm_R);
    Serial.print(" | State: ");
    if(motion_state.is_sharp_turn) Serial.print("SHARP_TURN");
    else if(motion_state.is_curve) Serial.print("CURVE");
    else Serial.print("STRAIGHT");
    Serial.print(" | Lost: ");
    Serial.print(lostCounter);
    Serial.print(" | AvgDev: ");
    Serial.print(motion_state.avg_deviation, 2);
    Serial.println("");
  }

  // 可以在这里添加其他非实时任务
  delay(10);
}